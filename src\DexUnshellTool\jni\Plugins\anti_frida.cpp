#include <string>
#include <dirent.h>
#include <errno.h>
#include <fcntl.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

#include <android/log.h>
#include <sys/syscall.h>
#include <sys/system_properties.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/inotify.h>
#include <sys/select.h>

#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/inotify.h>
#include <sys/select.h>

#include "anti_frida.h"
#include "TfdLog.h"

#define MAX_LENGTH 256
#define INOTIFY_EVENT_SIZE (sizeof(struct inotify_event))
#define INOTIFY_BUF_LEN (1024 * (INOTIFY_EVENT_SIZE + 16))

static const char *FRIDA_THREAD_GMAIN = "gmain";
static const char *FRIDA_NAMEDPIPE_LINJECTOR = "linjector";
static const char *PROC_STATUS = "/proc/self/task/%s/status";
static const char *PROC_FD = "/proc/self/fd";
static const char *PROC_TASK = "/proc/self/task";

// 全局变量用于控制监控
static volatile int maps_monitoring_active = 1;
static int inotify_fd = -1;
static int watch_fd = -1;

static ssize_t read_one_line(int fd, char *buf, unsigned int max_len) {
    char b;
    ssize_t ret;
    ssize_t bytes_read = 0;

    memset(buf, 0, max_len);
    do {
        ret = read(fd, &b, 1);
        if (ret != 1) {
            if (bytes_read == 0) {
                // error or EOF
                return -1;
            } else {
                return bytes_read;
            }
        }
        if (b == '\n') {
            return bytes_read;
        }
        *(buf++) = b;
        bytes_read += 1;
    } while (bytes_read < max_len - 1);
    return bytes_read;
}

// 检查maps文件内容是否包含frida相关内容
static int check_maps_for_frida(const char* maps_path) {
    FILE *fp;
    char line[1024];
    int found = 0;

    fp = fopen(maps_path, "r");
    if (fp != NULL) {
        while (fgets(line, sizeof(line), fp)) {
            if (strstr(line, "frida-agent") || strstr(line, "frida-agent-32") || strstr(line, "frida-agent-64")) {
                TFD_LOGI("Frida agent detected in maps: %s", line);
                found = 1;
                break;
            }
        }
        fclose(fp);
    } else {
        TFD_LOGI("Failed to open maps file: %s", maps_path);
    }
    return found;
}

static void detect_frida_namedpipe() {
    frida_check_port();
    DIR *dir = opendir(PROC_FD);
    if (dir != NULL) {
        struct dirent *entry = NULL;
        while ((entry = readdir(dir)) != NULL) {
            struct stat filestat;
            char buf[MAX_LENGTH] = "";
            char filePath[MAX_LENGTH] = "";
            snprintf(filePath, sizeof(filePath), "/proc/self/fd/%s", entry->d_name);
            lstat(filePath, &filestat);
            if ((filestat.st_mode & S_IFMT) == S_IFLNK) {
                //TODO: Another way is to check if filepath belongs to a path not related to system or the app
                // syscall(__NR_openat, AT_FDCWD, filePath, O_RDONLY | O_CLOEXEC, 0);
                // __NR_readlinkat
                syscall(__NR_readlinkat, AT_FDCWD, filePath, buf, MAX_LENGTH);
                // readlinkat(AT_FDCWD, filePath, buf, MAX_LENGTH);
                if (NULL != strstr(buf, FRIDA_NAMEDPIPE_LINJECTOR)) {
                    TFD_LOGI("anti frida success");
                    kill(getpid(), SIGKILL);
                }
            }

        }
    }
    closedir(dir);
}

void detect_frida_threads() {
    DIR *dir = opendir(PROC_TASK);
    struct dirent *entry = NULL;
    while ((entry = readdir(dir)) != NULL) {
        char filePath[MAX_LENGTH] = "";
        if (0 == strcmp(entry->d_name, ".") || 0 == strcmp(entry->d_name, "..")) {
            continue;
        }
        TFD_LOGI("while insert ");
        snprintf(filePath, sizeof(filePath), PROC_STATUS, entry->d_name);
        int fd = syscall(__NR_openat, AT_FDCWD, filePath, O_RDONLY | O_CLOEXEC, 0);
        if (fd != 0) {
            char buf[MAX_LENGTH] = "";
            read_one_line(fd, buf, MAX_LENGTH);
            TFD_LOGI("read_one_line insert ");
            if (strstr(buf, FRIDA_NAMEDPIPE_LINJECTOR) || strstr(buf, FRIDA_THREAD_GMAIN)) {
                TFD_LOGI("anti frida success");
                kill(getpid(), SIGKILL);
            }
            close(fd);
        }
    }
}

void frida_check_port(){
    struct sockaddr_in sa;
    memset(&sa, 0, sizeof(sa));
    sa.sin_family = AF_INET;
    inet_aton("127.0.0.1", &(sa.sin_addr));
    
    int sock;
    sock = socket(AF_INET, SOCK_STREAM, 0);
    sa.sin_port = htons(27042);
    if (connect(sock, (struct sockaddr*)&sa, sizeof(sa)) != -1) {
        close(sock);
        TFD_LOGI("anti frida success");
        kill(getpid(), SIGKILL);
    }
    close(sock);
}
// 基于inotify的优雅maps监控函数
void detect_frida_maps_with_inotify() {
    int pid = getpid();
    char maps_path[64];
    char buffer[INOTIFY_BUF_LEN];
    fd_set read_fds;
    struct timeval timeout;
    int select_result;

    if (pid < 0) {
        snprintf(maps_path, sizeof(maps_path), "/proc/self/maps");
    } else {
        snprintf(maps_path, sizeof(maps_path), "/proc/%d/maps", pid);
    }

    // 初始检查
    if (check_maps_for_frida(maps_path)) {
        TFD_LOGI("Frida detected during initial maps check");
        exit(0);
    }

    // 创建inotify实例
    inotify_fd = inotify_init();
    if (inotify_fd < 0) {
        TFD_LOGI("Failed to initialize inotify, falling back to polling");
        detect_frida_maps_fallback();
        return;
    }

    // 监控maps文件的修改事件
    watch_fd = inotify_add_watch(inotify_fd, maps_path, IN_MODIFY | IN_ATTRIB);
    if (watch_fd < 0) {
        TFD_LOGI("Failed to add inotify watch for %s, error: %s", maps_path, strerror(errno));
        close(inotify_fd);
        detect_frida_maps_fallback();
        return;
    }

    TFD_LOGI("Started inotify-based maps monitoring for: %s", maps_path);

    while (maps_monitoring_active) {
        FD_ZERO(&read_fds);
        FD_SET(inotify_fd, &read_fds);

        // 设置超时，避免无限阻塞
        timeout.tv_sec = 5;
        timeout.tv_usec = 0;

        select_result = select(inotify_fd + 1, &read_fds, NULL, NULL, &timeout);

        if (select_result > 0 && FD_ISSET(inotify_fd, &read_fds)) {
            // 有事件发生，读取事件
            int length = read(inotify_fd, buffer, INOTIFY_BUF_LEN);
            if (length > 0) {
                TFD_LOGI("Maps file changed, checking for frida...");
                if (check_maps_for_frida(maps_path)) {
                    TFD_LOGI("Frida detected after maps change");
                    cleanup_inotify();
                    exit(0);
                }
            }
        } else if (select_result == 0) {
            // 超时，进行一次检查以防遗漏
            if (check_maps_for_frida(maps_path)) {
                TFD_LOGI("Frida detected during timeout check");
                cleanup_inotify();
                exit(0);
            }
        } else if (select_result < 0) {
            TFD_LOGI("Select error: %s", strerror(errno));
            break;
        }
    }

    cleanup_inotify();
    TFD_LOGI("Maps monitoring stopped");
}

// 清理inotify资源
void cleanup_inotify() {
    if (watch_fd >= 0) {
        inotify_rm_watch(inotify_fd, watch_fd);
        watch_fd = -1;
    }
    if (inotify_fd >= 0) {
        close(inotify_fd);
        inotify_fd = -1;
    }
}

// 降级方案：轮询检测
void detect_frida_maps_fallback() {
    int pid = getpid();
    char maps_path[64];
    struct timespec sleep_time;
    sleep_time.tv_sec = 3;  // 降级时使用较长间隔
    sleep_time.tv_nsec = 0;

    if (pid < 0) {
        snprintf(maps_path, sizeof(maps_path), "/proc/self/maps");
    } else {
        snprintf(maps_path, sizeof(maps_path), "/proc/%d/maps", pid);
    }

    TFD_LOGI("Using fallback polling mode for maps detection");

    while (maps_monitoring_active) {
        if (check_maps_for_frida(maps_path)) {
            TFD_LOGI("Frida detected in fallback mode");
            exit(0);
        }
        nanosleep(&sleep_time, NULL);
    }
}

// 保持原有接口兼容性
void detect_frida_maps() {
    detect_frida_maps_with_inotify();
}

// 停止maps监控
void stop_maps_monitoring() {
    maps_monitoring_active = 0;
    TFD_LOGI("Maps monitoring stop requested");
}

void fridaServerDetection(){
    struct timespec timereq;
    timereq.tv_sec = 5;
    timereq.tv_nsec = 0;
    frida_check_port();
    detect_frida_maps();
    while (1) {
        // detect_frida_threads();
        detect_frida_namedpipe();
        nanosleep(&timereq, NULL);
    }
}

void antiFrida(){
    
    pthread_t t;
    
    int re = pthread_create(&t, NULL, (void*(*)(void*))fridaServerDetection, (void*)NULL);
    
    if(re < 0) {
        TFD_LOGI("failed to create antiFrida thread");
        return;
    }

    pthread_detach(t);
}