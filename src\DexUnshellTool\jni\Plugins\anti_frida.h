#ifndef ANTI_FRIDA_H
#define ANTI_FRIDA_H
#include <jni.h>

#ifdef __cplusplus
extern "C" {
#endif

void detect_frida_threads();
void detect_frida_maps();
void detect_frida_maps_with_inotify();
void detect_frida_maps_smart();
void detect_frida_maps_fallback();
void test_maps_inotify();
void cleanup_inotify();
void stop_maps_monitoring();
void antiFrida();
void frida_check_port();

#ifdef __cplusplus
}
#endif

#endif /* ANTI_FRIDA_H */
