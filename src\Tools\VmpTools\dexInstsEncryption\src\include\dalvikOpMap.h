// auto generate header
#ifndef DALVIK_OPMAP_H_
#define DALVIK_OPMAP_H_
const static unsigned char origOpToNewOp[256] = {0x90, 0xfb, 0xa5, 0x79, 0x82, 0x89, 0x05, 0x4b, 0x3f, 0x63, 0x32, 0x33, 0xd4, 0xff, 0x9d, 0x3b, 0x95, 0xe6, 0xb2, 0x0d, 0xfa, 0xa3, 0x70, 0x07, 0x7b, 0xc5, 0x42, 0x38, 0x78, 0xd5, 0x0c, 0x19, 0x40, 0xae, 0x15, 0x43, 0xa7, 0xea, 0x93, 0xe8, 0x8f, 0x56, 0xca, 0xfd, 0x36, 0x6c, 0xec, 0x4e, 0x3c, 0x9b, 0x71, 0xac, 0x2c, 0x39, 0xfe, 0xed, 0xc3, 0x2a, 0xef, 0x01, 0x98, 0xa1, 0xbe, 0x6a, 0xee, 0xb8, 0x54, 0xc1, 0x52, 0xb5, 0x83, 0xba, 0x1b, 0x51, 0x2f, 0xcb, 0x84, 0x60, 0x7c, 0x65, 0xdd, 0x0e, 0x3d, 0x0a, 0x28, 0x1c, 0x6e, 0xc6, 0x73, 0x49, 0x62, 0x94, 0x4a, 0x14, 0x87, 0x1d, 0x76, 0xe4, 0x10, 0x1e, 0x7f, 0x1a, 0x45, 0xe7, 0x7d, 0x5b, 0xe1, 0xd1, 0x77, 0xc8, 0xbf, 0xf0, 0x09, 0xd0, 0x57, 0x13, 0x5d, 0xaa, 0xb4, 0x24, 0xe0, 0x2d, 0x58, 0x2b, 0x91, 0x5c, 0x61, 0x81, 0xb3, 0x06, 0x0f, 0x29, 0x12, 0xce, 0xd9, 0x7a, 0x31, 0x88, 0x11, 0xa2, 0x8a, 0x16, 0xd8, 0xf5, 0xb7, 0x6b, 0x20, 0x92, 0x17, 0x27, 0xb6, 0x08, 0xcc, 0x44, 0xb0, 0xe9, 0x9a, 0xa6, 0xf6, 0x96, 0x6d, 0xbc, 0x9c, 0x74, 0x50, 0xdf, 0xc4, 0x8e, 0x2e, 0x53, 0x55, 0x00, 0x0b, 0xc2, 0x72, 0xcf, 0x75, 0xa4, 0xa0, 0x4c, 0x97, 0xeb, 0xd2, 0x69, 0x7e, 0x47, 0x9e, 0x34, 0x48, 0xf1, 0x21, 0x41, 0x5f, 0x99, 0xd3, 0xf3, 0xda, 0xaf, 0xa8, 0xad, 0xb1, 0x68, 0x64, 0xe3, 0xdc, 0x46, 0xd7, 0xcd, 0xa9, 0x8d, 0x80, 0x59, 0xbb, 0x25, 0x67, 0x86, 0xf7, 0x5e, 0xc7, 0x03, 0xde, 0xd6, 0x4d, 0xf9, 0xe2, 0x9f, 0x18, 0x35, 0x30, 0x22, 0x5a, 0xab, 0x66, 0xb9, 0x04, 0x02, 0xdb, 0x8c, 0xf4, 0xc0, 0x85, 0x6f, 0x3a, 0x4f, 0x8b, 0x1f, 0x3e, 0xc9, 0x26, 0x23, 0x37, 0xfc, 0xe5, 0xbd, 0xf8, 0xf2};
#endif
