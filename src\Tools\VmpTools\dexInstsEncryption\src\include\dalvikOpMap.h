// auto generate header
#ifndef DALVIK_OPMAP_H_
#define DALVIK_OPMAP_H_
const static unsigned char origOpToNewOp[256] = {0xcd, 0x39, 0x66, 0x07, 0x4d, 0xff, 0x11, 0xc3, 0xac, 0x77, 0xd7, 0xe2, 0xa1, 0x5d, 0x4e, 0x6b, 0x0a, 0x91, 0xbf, 0x49, 0x45, 0xd8, 0xcf, 0x08, 0xc6, 0x4c, 0x7f, 0x05, 0xed, 0x73, 0x14, 0x33, 0xdb, 0x92, 0x12, 0xf5, 0x25, 0x54, 0x3e, 0x1a, 0x3d, 0x59, 0x99, 0xeb, 0xa3, 0x6a, 0x51, 0x61, 0xdf, 0xb6, 0xb3, 0xd2, 0x06, 0xf9, 0xd1, 0xf7, 0x43, 0x71, 0x83, 0xef, 0xe5, 0x8d, 0x69, 0x9e, 0xf8, 0x0d, 0x7a, 0xee, 0x6c, 0x18, 0x85, 0xd6, 0xe9, 0x13, 0xb1, 0x42, 0x2a, 0x3f, 0xa4, 0xa9, 0x76, 0xa6, 0x04, 0xec, 0x2d, 0x24, 0x0e, 0x5b, 0x9b, 0xa2, 0x1d, 0xae, 0x95, 0x6d, 0xb8, 0x1f, 0x0b, 0x62, 0xf1, 0xfd, 0xca, 0x29, 0x5f, 0xe8, 0x48, 0x03, 0xad, 0x67, 0x31, 0x0f, 0x20, 0x27, 0xfe, 0x15, 0x7c, 0x6e, 0xab, 0xe6, 0xde, 0xc4, 0x8e, 0x36, 0x32, 0x94, 0x93, 0x8a, 0xb0, 0x4a, 0xbc, 0x41, 0x63, 0x80, 0x8c, 0x9a, 0x7e, 0xb9, 0x5e, 0x60, 0xd9, 0x89, 0x40, 0xb4, 0xa8, 0x58, 0xd4, 0x1b, 0x79, 0x50, 0xcc, 0x88, 0x87, 0x09, 0x7d, 0xaa, 0x26, 0x68, 0x47, 0x3a, 0x21, 0xf2, 0xdd, 0xa0, 0xcb, 0x2b, 0x70, 0x34, 0x3b, 0x22, 0x82, 0x6f, 0x01, 0x35, 0x5c, 0x86, 0xfc, 0x81, 0x74, 0x1e, 0x8f, 0xe0, 0x00, 0xf6, 0x90, 0x38, 0x98, 0x97, 0x16, 0x10, 0xd5, 0x46, 0xf3, 0xda, 0x17, 0xf4, 0x5a, 0xe4, 0xea, 0x2e, 0x65, 0x57, 0x4f, 0xfb, 0x28, 0x96, 0xa7, 0x53, 0x02, 0xc1, 0x19, 0x72, 0xc5, 0x44, 0x64, 0xa5, 0x1c, 0x0c, 0x2f, 0xaf, 0xc8, 0xc0, 0xd3, 0xb7, 0x3c, 0xb5, 0xfa, 0xf0, 0x9d, 0xbe, 0xc9, 0x30, 0xe1, 0x52, 0xbb, 0xbd, 0xc7, 0xdc, 0xe3, 0x7b, 0x2c, 0x55, 0xe7, 0x9c, 0xb2, 0x84, 0x9f, 0x78, 0xce, 0x75, 0x23, 0xd0, 0xc2, 0x37, 0x8b, 0xba, 0x4b, 0x56};
#endif
