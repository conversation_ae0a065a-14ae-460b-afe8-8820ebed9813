// auto generate header
#ifndef DALVIK_OPMAP_H_
#define DALVIK_OPMAP_H_
const static unsigned char origOpToNewOp[256] = {0xc4, 0x25, 0x84, 0x14, 0x44, 0xd1, 0x03, 0x30, 0x27, 0xb0, 0xad, 0xd5, 0xf0, 0xd0, 0x49, 0xb6, 0x12, 0xe9, 0xe4, 0x8a, 0x96, 0xdc, 0x7a, 0x0c, 0x22, 0x5d, 0x3d, 0xc9, 0x35, 0xe7, 0xa9, 0xc6, 0x5e, 0xa2, 0x57, 0x75, 0x02, 0x7b, 0xc2, 0x07, 0xf2, 0xfd, 0x06, 0x5a, 0x2c, 0x51, 0x72, 0x90, 0xa4, 0x19, 0x4b, 0xa0, 0xef, 0x16, 0x81, 0x46, 0xf1, 0x7e, 0xe0, 0xae, 0xcd, 0xf6, 0x8f, 0x8c, 0xa1, 0xda, 0xde, 0x6a, 0x76, 0x7c, 0xfc, 0x3f, 0x77, 0xb9, 0x3a, 0x55, 0x31, 0xca, 0x45, 0x6b, 0x23, 0xcb, 0x9a, 0x61, 0xd9, 0x67, 0xc7, 0x91, 0x9f, 0x11, 0x2f, 0x32, 0x56, 0x58, 0x34, 0xa3, 0xc5, 0x85, 0xe6, 0xc3, 0x6f, 0xab, 0x63, 0x99, 0x28, 0x3e, 0x60, 0xce, 0x40, 0xfa, 0xb7, 0x42, 0x08, 0x7d, 0x17, 0x82, 0xf8, 0x74, 0x89, 0x98, 0x94, 0xbd, 0xb2, 0xed, 0x6c, 0x50, 0x0d, 0x1e, 0x73, 0xbe, 0x36, 0x92, 0x79, 0xf7, 0xaa, 0xf3, 0xe1, 0x4e, 0x0e, 0x66, 0x01, 0x2d, 0x1d, 0xfb, 0x37, 0xf4, 0x00, 0xea, 0xd2, 0x41, 0xe5, 0x20, 0x10, 0x5b, 0x1b, 0xf9, 0x1a, 0x4c, 0x83, 0xa5, 0x4d, 0x64, 0xdf, 0xa6, 0x93, 0x65, 0xdd, 0xff, 0x13, 0xa7, 0x9d, 0x2b, 0xd8, 0x5c, 0xaf, 0x70, 0xba, 0x39, 0x09, 0x9b, 0x8b, 0xc1, 0xb3, 0xeb, 0x52, 0x9e, 0x38, 0xac, 0x2e, 0xc8, 0xa8, 0xb8, 0x3b, 0x88, 0xcc, 0x18, 0x59, 0x53, 0x4a, 0x8d, 0x54, 0xcf, 0x86, 0xee, 0x26, 0xbc, 0xbb, 0xd7, 0x97, 0x48, 0x43, 0xfe, 0x3c, 0x0f, 0x87, 0xb4, 0xbf, 0xd3, 0xdb, 0x0a, 0x95, 0x24, 0x6e, 0x0b, 0xe2, 0x1c, 0xd6, 0x71, 0xe8, 0xec, 0xe3, 0x15, 0x7f, 0x9c, 0x2a, 0xb1, 0x47, 0x21, 0x5f, 0x68, 0xc0, 0x33, 0xb5, 0x8e, 0xd4, 0x4f, 0x78, 0x05, 0x6d, 0xf5, 0x69, 0x62, 0x80, 0x29, 0x04, 0x1f};
#endif
