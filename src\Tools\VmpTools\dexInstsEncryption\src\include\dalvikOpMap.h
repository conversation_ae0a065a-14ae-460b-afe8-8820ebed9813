// auto generate header
#ifndef DALVIK_OPMAP_H_
#define DALVIK_OPMAP_H_
const static unsigned char origOpToNewOp[256] = {0x58, 0xb7, 0xa1, 0xe8, 0x50, 0xcf, 0x6d, 0xb9, 0x0c, 0xa2, 0x93, 0x5c, 0x03, 0x19, 0xe9, 0xbd, 0x3c, 0x02, 0x5f, 0x51, 0x17, 0x13, 0xbe, 0xae, 0x9f, 0x7b, 0xd5, 0x3a, 0x74, 0x25, 0x20, 0xed, 0xaf, 0x70, 0xe7, 0x69, 0xad, 0xdc, 0xb3, 0x99, 0x89, 0xc9, 0x00, 0xf5, 0xd4, 0x8f, 0xfd, 0x66, 0x79, 0x22, 0x6c, 0xee, 0x5b, 0xb5, 0xcc, 0xf1, 0x36, 0x59, 0x10, 0x56, 0xff, 0x8b, 0x88, 0xf6, 0x6a, 0x14, 0x04, 0x4e, 0x4a, 0xb0, 0xf4, 0x45, 0xea, 0x05, 0x1f, 0xb8, 0x82, 0x08, 0xa9, 0xe1, 0x97, 0xbf, 0x6f, 0xfa, 0xcb, 0xd0, 0x72, 0x16, 0xeb, 0x7d, 0x80, 0x1e, 0x61, 0x47, 0x8c, 0x42, 0xde, 0x76, 0x2d, 0x9d, 0x62, 0xb4, 0x06, 0x94, 0x7a, 0x31, 0x48, 0x95, 0x38, 0x09, 0xdf, 0xf2, 0xda, 0xdb, 0x53, 0xc5, 0x37, 0x54, 0xd2, 0xd8, 0x3f, 0x39, 0x3d, 0x71, 0x12, 0x0d, 0xdd, 0x92, 0x5a, 0xfe, 0x68, 0xa5, 0xd6, 0xca, 0x3e, 0xa4, 0xc6, 0x41, 0x96, 0xa0, 0xb1, 0xab, 0xb2, 0xcd, 0xe6, 0x27, 0x2f, 0x87, 0x5e, 0x86, 0x63, 0x78, 0xd9, 0x4b, 0x91, 0xef, 0xf8, 0x7f, 0xbc, 0xfc, 0xc8, 0x4f, 0x1b, 0x60, 0x8d, 0xe5, 0xce, 0x23, 0x2c, 0x6b, 0x0f, 0xa6, 0x6e, 0x15, 0x90, 0xc4, 0x8a, 0x01, 0x8e, 0x24, 0x0a, 0x52, 0x9a, 0xaa, 0x5d, 0xac, 0x28, 0xc7, 0xa3, 0x9b, 0x2a, 0xc1, 0x9e, 0xf9, 0x7c, 0x1a, 0x3b, 0x30, 0x67, 0xa8, 0x26, 0x4c, 0x46, 0x85, 0xc2, 0xec, 0x34, 0xe3, 0xd1, 0xc0, 0x81, 0x65, 0x44, 0x18, 0xf3, 0xe4, 0x49, 0x55, 0x07, 0xb6, 0x33, 0xfb, 0x83, 0x4d, 0x77, 0xd7, 0x0e, 0x75, 0x1c, 0x73, 0x7e, 0x2b, 0xd3, 0xf0, 0xf7, 0x84, 0x32, 0x64, 0x98, 0x9c, 0xbb, 0x57, 0xc3, 0x0b, 0xba, 0x35, 0xa7, 0x1d, 0x2e, 0x40, 0x43, 0x21, 0xe0, 0xe2, 0x29, 0x11};
#endif
